import { log } from "../utils/log"
import { MainMenuPage } from "./pages/main.menu"
import { WalletListPage } from "./pages/wallet.list"
import { WalletDetailsPage } from "./pages/wallet.details"
import { WalletCreatePage } from "./pages/wallet.create"
import { WalletImportPage } from "./pages/wallet.import"
import { WalletExportPage } from "./pages/wallet.export"
import { WalletDeletePage } from "./pages/wallet.delete"
import { SettingsMainPage } from "./pages/settings.main"
import { HelpSectionsPage } from "./pages/help.sections"
import { AccountStatsPage } from "./pages/account.stats"
import { ChainInfoPage } from "./pages/chain.info"

/**
 * Callback Query Distributor
 * Distributes callback queries to appropriate page classes for enhanced maintainability
 */
export class CallbackDistributor {
  // Page instances for callback distribution
  public readonly mainMenuPage = new MainMenuPage()
  public readonly walletListPage = new WalletListPage()
  public readonly walletDetailsPage = new WalletDetailsPage()
  public readonly walletCreatePage = new WalletCreatePage()
  public readonly walletImportPage = new WalletImportPage()
  public readonly walletExportPage = new WalletExportPage()
  public readonly walletDeletePage = new WalletDeletePage()
  public readonly settingsMainPage = new SettingsMainPage()
  public readonly helpSectionsPage = new HelpSectionsPage()
  public readonly accountStatsPage = new AccountStatsPage()
  public readonly chainInfoPage = new ChainInfoPage()

  /**
   * Distribute callback query to appropriate page class
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not handled by any page
   */
  async distributeCallback(ctx: any, callbackData: string): Promise<boolean> {
    try {
      // Try each page class in logical order
      const pageHandlers = [this.mainMenuPage, this.walletListPage, this.walletDetailsPage, this.walletCreatePage, this.walletImportPage, this.walletExportPage, this.walletDeletePage, this.settingsMainPage, this.helpSectionsPage, this.accountStatsPage, this.chainInfoPage]

      // Iterate through page handlers until one handles the callback
      for (const pageHandler of pageHandlers) {
        if (await pageHandler.handleCallback(ctx, callbackData)) {
          return true
        }
      }

      // Handle special global callbacks that don't belong to specific pages
      if (callbackData === "cancel") {
        await this.handleCancel(ctx)
        return true
      } else if (callbackData === "noop") {
        // No-operation: completely non-interactive button
        return true
      }

      return false
    } catch (error) {
      log.error(`Error in CallbackDistributor.distributeCallback: ${error}`)
      return false
    }
  }

  /**
   * Handle cancel callback
   * @param ctx Callback query context
   */
  private async handleCancel(ctx: any): Promise<void> {
    // Delegate cancel handling to main menu page
    // The main menu page has access to protected methods and can handle the full cancel flow
    await this.mainMenuPage.handleCallback(ctx, "cancel")
  }
}
