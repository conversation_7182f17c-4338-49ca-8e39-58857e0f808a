import { Bo<PERSON>, InlineKeyboard } from "grammy"
import { log } from "../../utils/log"
import { User } from "../../models"
import { BotUtils } from "../baseHandler"

/**
 * Tel<PERSON><PERSON> Handler
 * Handles all Telegram-specific operations including bot initialization,
 * callback queries, message handling, and user management
 */
export class TelegramHandler {
  private bot: Bot

  constructor(token: string) {
    this.bot = new Bot(token)
    this.initializeBot()
  }

  /**
   * Initialize bot with event handlers
   */
  private initializeBot(): void {
    // Handle callback queries from inline keyboards
    this.bot.on("callback_query:data", this.handleCallbackQuery.bind(this))

    // Handle text messages
    this.bot.on("message:text", this.handleTextMessage.bind(this))

    // Error handling
    this.bot.catch((err) => {
      log.error(`Telegram bot error: ${JSON.stringify(err)}`)
    })

    // Start bot
    this.bot.start({
      onStart(botInfo) {
        log.info(`Telegram bot started: ${botInfo.username}`)
      }
    })
  }

  /**
   * Handle callback queries from inline keyboards
   * @param ctx Callback query context
   */
  private async handleCallbackQuery(ctx: any): Promise<void> {
    try {
      await ctx.answerCallbackQuery()

      const callbackData = ctx.callbackQuery.data

      // Emit callback event for external handling with complete callback data
      await this.onCallbackQuery(ctx, callbackData)
    } catch (error) {
      log.error(`Error handleCallbackQuery: ${error}`)
      await BotUtils.answerCallback(ctx, "callback_error_general")
      await BotUtils.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle text messages
   * @param ctx Message context
   * @param messageHandler Function to handle the message
   */
  private async handleTextMessage(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const user = await this.getOrCreateUser(telegramId, ctx.from?.username, ctx.from?.first_name)

      if (!user) {
        const keyboard = new InlineKeyboard().text("🔄 Try Again", "main_menu")
        await BotUtils.replyWithKeyboard(ctx, "user_creation_failed", keyboard)
        return
      }

      // Check if user has an active session
      const hasSession = await BotUtils.sessionHas(telegramId)
      if (hasSession) {
        await this.onSessionInput(ctx, telegramId)
        return
      }

      // No active session - emit event for external handling
      await this.onTextMessage(ctx, user)
    } catch (error) {
      log.error(`Error handleTextMessage: ${error}`)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await BotUtils.replyWithKeyboard(ctx, "general_error", keyboard)
    }
  }

  /**
   * Get or create user
   * @param telegramId Telegram user ID
   * @param username Username
   * @param firstName First name
   * @returns User object or null
   */
  private async getOrCreateUser(telegramId: number, username?: string, firstName?: string): Promise<any> {
    return await User.getOrCreate(telegramId, username || `user_${telegramId}`, firstName || "Unknown")
  }

  /**
   * Handle session input
   * @param ctx Message context
   * @param userId User ID
   */
  private async onSessionInput(ctx: any, userId: number): Promise<void> {
    const session = await BotUtils.sessionGet(userId)
    if (!session || !session.state) {
      await BotUtils.sessionDelete(userId)
      await this.onNoSession(ctx)
      return
    }

    const userInput = ctx.message?.text?.trim()
    if (!userInput) {
      const keyboard = new InlineKeyboard().text("❌ Cancel", "cancel")
      await BotUtils.replyWithKeyboard(ctx, "input_invalid_empty", keyboard)
      return
    }

    // Emit session input event for external handling
    await this.onSessionInputReceived(ctx, userId, userInput, session)
  }

  /**
   * Event handlers (to be overridden by implementing class)
   */
  protected async onCallbackQuery(ctx: any, callbackData: string): Promise<void> {
    // Override in implementing class
    log.warn(`Unhandled callback query: ${callbackData}`)
  }

  protected async onTextMessage(ctx: any, user: any): Promise<void> {
    // Override in implementing class
    log.warn(`Unhandled text message from user: ${user.id}`)
  }

  protected async onSessionInputReceived(ctx: any, userId: number, userInput: string, session: any): Promise<void> {
    // Override in implementing class
    log.warn(`Unhandled session input from user: ${userId}`)
  }

  protected async onNoSession(ctx: any): Promise<void> {
    // Override in implementing class
    log.warn(`No session handler defined`)
  }
}
