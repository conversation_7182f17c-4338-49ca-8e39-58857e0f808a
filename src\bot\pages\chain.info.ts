import { InlineKeyboard } from "grammy"
import { BotUtils } from "../baseHandler"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Chain Information Page
 * Handles blockchain chain information display
 */
export class ChainInfoPage {
  /**
   * Create chain info keyboard
   * @param chain Chain name
   */
  private static createChainInfoKeyboard(chain: string): InlineKeyboard {
    return new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("📊 Network Stats", `chain_detail:stats:${chain}`).row().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Handle callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    if (callbackData.startsWith("chain_select:")) {
      const chain = callbackData.split(":")[1]
      if (chain) {
        await this.show(ctx, chain)
        return true
      }
    } else if (callbackData.startsWith("chain_detail:fees:")) {
      const chain = callbackData.split(":")[2] as string
      await this.showFees(ctx, chain)
      return true
    } else if (callbackData.startsWith("chain_detail:stats:")) {
      const chain = callbackData.split(":")[2] as string
      await this.showStats(ctx, chain)
      return true
    }
    return false
  }

  /**
   * Show chain information
   * @param ctx Callback query context
   * @param chain Chain name
   */
  async show(ctx: any, chain: string): Promise<void> {
    const { chainId, symbol, chainDisplayName } = BlockchainConfig.get[chain]

    const keyboard = ChainInfoPage.createChainInfoKeyboard(chain)

    await BotUtils.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: chainDisplayName,
      symbol,
      chainId,
      chain
    })
  }

  /**
   * Show chain fees information
   * @param ctx Callback query context
   * @param chain Chain name
   */
  async showFees(ctx: any, chain: string): Promise<void> {
    const { chainId, symbol, chainDisplayName } = BlockchainConfig.get[chain]

    const keyboard = new InlineKeyboard().text("🔙 Back to Chain", `chain_select:${chain}:info`)

    // For now, show basic chain info as fees
    await BotUtils.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: chainDisplayName,
      symbol,
      chainId,
      chain
    })
  }

  /**
   * Show chain statistics
   * @param ctx Callback query context
   * @param chain Chain name
   */
  async showStats(ctx: any, chain: string): Promise<void> {
    const { chainId, symbol, chainDisplayName } = BlockchainConfig.get[chain]

    const keyboard = new InlineKeyboard().text("💰 View Fees", `chain_detail:fees:${chain}`).text("🔙 Back to Chain", `chain_select:${chain}:info`)

    // For now, show basic chain info as stats
    await BotUtils.updateKeyboard(ctx, "chain_info", keyboard, {
      chainName: chainDisplayName,
      symbol,
      chainId,
      chain
    })
  }
}
