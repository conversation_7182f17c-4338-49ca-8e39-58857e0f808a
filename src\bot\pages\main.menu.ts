import { InlineKeyboard } from "grammy"
import { BotUtils } from "../baseHandler"

/**
 * Main Menu Page
 * Handles the main menu display and navigation
 */
export class MainMenuPage {
  /**
   * Create main menu keyboard
   */
  private static createKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("💼 My Wallets", "wallet_action:list").text("📊 View Stats", "wallet_action:stats").row().text("⚙️ Settings", "wallet_action:settings").text("❓ Help", "wallet_action:help")
  }

  /**
   * Handle callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    if (callbackData === "main_menu" || callbackData === "back:main_menu") {
      await this.show(ctx)
      return true
    } else if (callbackData === "cancel") {
      await this.handleCancel(ctx)
      return true
    }
    return false
  }

  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = MainMenuPage.createKeyboard()
    await BotUtils.updateKeyboard(ctx, "start", keyboard)
  }

  /**
   * Handle cancel callback - clear session and show main menu
   * @param ctx Callback query context
   */
  private async handleCancel(ctx: any): Promise<void> {
    const userId = ctx.from?.id as number

    // Clear any active session
    await BotUtils.sessionDelete(userId)

    // Acknowledge the callback
    await BotUtils.answerCallback(ctx, "callback_cancelled")

    // Delete the keyboard
    await BotUtils.deleteKeyboard(ctx)

    // Show main menu after cancellation
    await this.show(ctx)
  }
}
