import { InlineKeyboard } from "grammy"
import { <PERSON><PERSON>and<PERSON> } from "../baseHandler"

/**
 * Main Menu Page
 * Handles the main menu display and navigation
 */
export class MainMenuPage extends BaseHandler {
  /**
   * Create main menu keyboard
   */
  private static createKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("💼 My Wallets", "wallet_action:list").text("📊 View Stats", "wallet_action:stats").row().text("⚙️ Settings", "wallet_action:settings").text("❓ Help", "wallet_action:help")
  }

  /**
   * Handle callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    if (callbackData === "main_menu" || callbackData === "back:main_menu") {
      await this.show(ctx)
      return true
    }
    return false
  }

  /**
   * Show main menu with inline keyboard
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = MainMenuPage.createKeyboard()
    await this.updateKeyboard(ctx, "start", keyboard)
  }
}
