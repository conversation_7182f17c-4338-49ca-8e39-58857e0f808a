import { InlineKeyboard } from "grammy"
import { BotUtils } from "../baseHandler"

/**
 * Settings Main Page
 * Handles main settings menu display
 */
export class SettingsMainPage {
  /**
   * Create settings keyboard
   */
  private static createKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🌐 Language", "settings:language").text("🔔 Notifications", "settings:notifications").row().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Create back to settings keyboard
   */
  private static createBackToSettingsKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Settings", "wallet_action:settings").text("🏠 Main Menu", "back:main_menu")
  }

  /**
   * <PERSON>le callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    if (callbackData === "wallet_action:settings") {
      await this.show(ctx)
      return true
    } else if (callbackData === "settings:language") {
      await this.showLanguage(ctx)
      return true
    } else if (callbackData === "settings:notifications") {
      await this.showNotifications(ctx)
      return true
    }
    return false
  }

  /**
   * Show settings menu
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = SettingsMainPage.createKeyboard()
    await BotUtils.updateKeyboard(ctx, "settings_menu", keyboard)
  }

  /**
   * Show language settings page
   * @param ctx Callback query context
   */
  async showLanguage(ctx: any): Promise<void> {
    const keyboard = SettingsMainPage.createBackToSettingsKeyboard()
    await BotUtils.updateKeyboard(ctx, "settings_language", keyboard)
  }

  /**
   * Show notification settings page
   * @param ctx Callback query context
   */
  async showNotifications(ctx: any): Promise<void> {
    const keyboard = SettingsMainPage.createBackToSettingsKeyboard()
    await BotUtils.updateKeyboard(ctx, "settings_notifications", keyboard)
  }
}
