import { InlineKeyboard } from "grammy"
import { BotUtils } from "../baseHandler"
import { User, Wallet } from "../../models"
import { BlockchainWallet } from "../../blockchain/wallet"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Wallet Creation Page
 * Handles wallet creation flow and chain selection
 */
export class WalletCreatePage {
  /**
   * Create chain selection keyboard
   * @param callbackPrefix Callback prefix for chain selection
   * @param maxChains Maximum number of chains to show
   */
  private static createChainSelectionKeyboard(callbackPrefix: string, maxChains: number = 6): InlineKeyboard {
    const keyboard = new InlineKeyboard()
    WalletCreatePage.addChainButtons(keyboard, callbackPrefix, maxChains)
    keyboard.row().text("🔙 Back to Menu", "back:main_menu")
    return keyboard
  }

  /**
   * Add chain selection buttons to keyboard dynamically
   * @param keyboard InlineKeyboard instance to add buttons to
   * @param callbackPrefix Prefix for callback data (e.g., "create_wallet" or "import_chain")
   * @param maxChains Maximum number of chains to show (default: 6)
   */
  private static addChainButtons(keyboard: InlineKeyboard, callbackPrefix: string, maxChains: number = 6): void {
    const chains = BlockchainConfig.listName.slice(0, maxChains)
    for (let i = 0; i < chains.length; i += 2) {
      keyboard.row()
      const chain1 = chains[i]
      const chain2 = chains[i + 1]

      // Add first chain button
      if (chain1 && BlockchainConfig.get[chain1]) {
        const { chainDisplayName } = BlockchainConfig.get[chain1]
        keyboard.text(`🔗 ${chainDisplayName}`, `${callbackPrefix}:${chain1}`)
      }

      // Add second chain button if exists
      if (chain2 && BlockchainConfig.get[chain2]) {
        const { chainDisplayName } = BlockchainConfig.get[chain2]
        keyboard.text(`🔗 ${chainDisplayName}`, `${callbackPrefix}:${chain2}`)
      }
    }
  }

  /**
   * Handle callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    if (callbackData === "wallet_action:create") {
      await this.show(ctx)
      return true
    } else if (callbackData.startsWith("create_wallet:")) {
      const chain = callbackData.split(":")[1]
      if (chain) {
        const userId = ctx.from?.id as number
        await this.showNamePrompt(ctx, chain, userId)
        return true
      }
    }
    return false
  }

  /**
   * Show create wallet form with chain selection
   * @param ctx Callback query context
   */
  async show(ctx: any): Promise<void> {
    const keyboard = WalletCreatePage.createChainSelectionKeyboard("create_wallet")
    await BotUtils.updateKeyboard(ctx, "createwallet_usage", keyboard)
  }

  /**
   * Show wallet name prompt for selected chain
   * @param ctx Callback query context
   * @param chain Selected blockchain chain
   * @param userId User's Telegram ID
   */
  async showNamePrompt(ctx: any, chain: string, userId: number): Promise<void> {
    // Set up session to wait for wallet name input directly
    await BotUtils.sessionSet(userId, {
      state: "waiting_wallet_name",
      chain: chain,
      operation: "create_wallet"
    })

    const keyboard = new InlineKeyboard().text("🔙 Back to Chains", "wallet_action:create").text("❌ Cancel", "cancel")

    const { symbol, chainDisplayName } = BlockchainConfig.get[chain]
    await BotUtils.updateKeyboard(ctx, "createwallet_name_prompt", keyboard, {
      chainName: chainDisplayName,
      symbol
    })
  }

  /**
   * Show wallet creation success page
   * @param ctx Message context
   * @param wallet Created wallet object
   * @param chain Blockchain chain
   */
  async showSuccess(ctx: any, wallet: any, chain: string): Promise<void> {
    const successKeyboard = new InlineKeyboard().text("👁️ View Wallet", `wallet_action:view:${wallet.id}`).text("➕ Create Another", `create_wallet:${chain}`).row().text("🏠 Back to Main Menu", "main_menu")

    const { chainDisplayName } = BlockchainConfig.get[chain]
    await BotUtils.replyWithKeyboard(ctx, "createwallet_success", successKeyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }

  /**
   * Show wallet creation failure page
   * @param ctx Message context
   * @param chain Blockchain chain
   * @param errorType Type of error (duplicate_name, duplicate_address, general)
   * @param address Optional address for duplicate address error
   */
  async showFailure(ctx: any, chain: string, errorType: string, address?: string): Promise<void> {
    let keyboard: InlineKeyboard
    let contentKey: string

    switch (errorType) {
      case "duplicate_name":
        keyboard = new InlineKeyboard().text("🔙 Back to Chains", "wallet_action:create").text("❌ Cancel", "cancel")
        contentKey = "wallet_name_exists"
        break

      case "duplicate_address":
        keyboard = new InlineKeyboard().text("🔄 Try Again", `create_wallet:${chain}`).text("🏠 Back to Main Menu", "main_menu")
        const { chainDisplayName } = BlockchainConfig.get[chain]
        await BotUtils.replyWithKeyboard(ctx, "importwallet_duplicate_address", keyboard, {
          chainName: chainDisplayName,
          address: address
        })
        return

      default:
        keyboard = new InlineKeyboard().text("🔄 Try Again", `create_wallet:${chain}`).text("🏠 Back to Main Menu", "main_menu")
        contentKey = "createwallet_failed"
    }

    await BotUtils.replyWithKeyboard(ctx, contentKey, keyboard)
  }

  /**
   * Process wallet creation with provided name
   * @param ctx Message context
   * @param userId User's Telegram ID
   * @param walletName Wallet name
   * @param chain Blockchain chain
   */
  async processCreation(ctx: any, userId: number, walletName: string, chain: string): Promise<void> {
    try {
      const user = await User.getById(userId)
      if (!user) {
        await BotUtils.sessionDelete(userId)
        const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
        await BotUtils.replyWithKeyboard(ctx, "user_not_found", keyboard)
        return
      }

      // Generate wallet address and private key
      const { address, privateKey } = new BlockchainWallet(chain).generate()

      // Get chain ID from blockchain config
      const { chainId } = BlockchainConfig.get[chain]

      // Create the wallet with encryption
      const wallet = await Wallet.create(user.id, walletName, chain, chainId, address, privateKey, "system", BotUtils.getPasswordWallet())

      await BotUtils.sessionDelete(userId)

      if (wallet && typeof wallet === "object") {
        await this.showSuccess(ctx, wallet, chain)
      } else if (wallet === "DUPLICATE_NAME") {
        await this.showFailure(ctx, chain, "duplicate_name")
      } else if (wallet === "DUPLICATE_ADDRESS") {
        await this.showFailure(ctx, chain, "duplicate_address", address)
      } else {
        await this.showFailure(ctx, chain, "general")
      }
    } catch (error) {
      log.error(`Error WalletCreatePage.processCreation: ${error}`)
      await BotUtils.sessionDelete(userId)

      const errorKeyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await BotUtils.replyWithKeyboard(ctx, "createwallet_error", errorKeyboard)
    }
  }
}
