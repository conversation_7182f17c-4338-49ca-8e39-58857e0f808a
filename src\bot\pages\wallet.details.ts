import { InlineKeyboard } from "grammy"
import { BotUtils } from "../baseHandler"
import { Wallet } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"

/**
 * Wallet Details Page
 * Handles individual wallet details display and actions
 */
export class WalletDetailsPage {
  /**
   * Create wallet details keyboard
   * @param walletId Wallet ID
   */
  private static createKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard()
      .text("💰 Balance", `wallet_detail:balance:${walletId}`)
      .text("📊 History", `wallet_detail:history:${walletId}`)
      .row()
      .text("📈 Stats", `wallet_detail:stats:${walletId}`)
      .text("🔑 Export Key", `wallet_detail:export:${walletId}`)
      .row()
      .text("🗑️ Delete", `wallet_detail:delete:${walletId}`)
      .text("🔙 Back to Wallets", "wallet_action:list")
  }

  /**
   * Create back to wallet keyboard
   * @param walletId Wallet ID
   */
  private static createBackToWalletKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }

  /**
   * Create export confirmation keyboard
   * @param walletId Wallet ID
   */
  private static createExportConfirmationKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔑 Yes, Export Key", `export_confirm:${walletId}`).text("🔙 Cancel", `wallet_select:${walletId}`)
  }

  /**
   * Create delete confirmation keyboard
   * @param walletId Wallet ID
   */
  private static createDeleteConfirmationKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("✅ Yes, Delete", `confirm_delete:${walletId}`).text("❌ Cancel", "cancel").row().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }

  /**
   * Handle callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    // Handle wallet selection
    if (callbackData.startsWith("wallet_select:")) {
      const walletId = parseInt(callbackData.split(":")[1] || "0")
      if (!isNaN(walletId) && walletId > 0) {
        const telegramId = ctx.from?.id as number
        await this.show(ctx, walletId, telegramId)
        return true
      }
    }
    // Handle wallet detail actions
    else if (callbackData.startsWith("wallet_detail:")) {
      const userId = ctx.from?.id as number
      const parts = callbackData.split(":")
      const route = parts[1]
      const walletId = parseInt(parts[2] || "0")
      const wallet = await Wallet.getByIdForOwner(walletId, userId)

      if (!wallet) {
        await BotUtils.answerCallback(ctx, "callback_wallet_access_denied")
        await BotUtils.deleteKeyboard(ctx)
        return true
      }

      // Import required page classes for wallet detail actions
      const { WalletBalancePage } = await import("./wallet.balance")
      const { WalletHistoryPage } = await import("./wallet.history")
      const { WalletStatsPage } = await import("./wallet.stats")
      const { WalletExportPage } = await import("./wallet.export")
      const { WalletDeletePage } = await import("./wallet.delete")

      if (route === "balance") {
        const balancePage = new WalletBalancePage()
        await balancePage.show(ctx, wallet)
      } else if (route === "history") {
        const historyPage = new WalletHistoryPage()
        await historyPage.show(ctx, wallet)
      } else if (route === "stats") {
        const statsPage = new WalletStatsPage()
        await statsPage.show(ctx, wallet)
      } else if (route === "export") {
        const exportPage = new WalletExportPage()
        await exportPage.showConfirmation(ctx, wallet)
      } else if (route === "delete") {
        const deletePage = new WalletDeletePage()
        await deletePage.showConfirmation(ctx, wallet)
      }
      return true
    }
    return false
  }
  /**
   * Show wallet details with action buttons
   * @param ctx Callback query context
   * @param walletId Wallet ID
   * @param telegramId User's Telegram ID for ownership validation
   */
  async show(ctx: any, walletId: number, telegramId: number): Promise<void> {
    const wallet = await Wallet.getByIdForOwner(walletId, telegramId)
    if (!wallet) {
      await BotUtils.answerCallback(ctx, "callback_wallet_access_denied")
      await BotUtils.deleteKeyboard(ctx)
      return
    }

    // Show wallet details with action buttons
    const keyboard = WalletDetailsPage.createKeyboard(walletId)

    const { symbol, chainDisplayName } = BlockchainConfig.get[wallet.chain]
    const balance = wallet.balance.toString()
    const createdDate = wallet.createdAt?.toLocaleDateString() || "Unknown"

    await BotUtils.updateKeyboard(ctx, "wallet_info", keyboard, {
      name: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address,
      balance,
      symbol,
      chainId: wallet.chainId,
      createdDate,
      createdBy: wallet.createdBy
    })
  }
}
