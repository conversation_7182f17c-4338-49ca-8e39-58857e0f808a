import { InlineKeyboard } from "grammy"
import { BotUtils } from "../baseHandler"
import { Wallet } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Wallet Export Page
 * Handles wallet private key export with security warnings
 */
export class WalletExportPage {
  /**
   * Create export confirmation keyboard
   * @param walletId Wallet ID
   */
  private static createExportConfirmationKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔑 Yes, Export Key", `export_confirm:${walletId}`).text("🔙 Cancel", `wallet_select:${walletId}`)
  }

  /**
   * Create back to wallet keyboard
   * @param walletId Wallet ID
   */
  private static createBackToWalletKeyboard(walletId: number): InlineKeyboard {
    return new InlineKeyboard().text("🔙 Back to Wallet", `wallet_select:${walletId}`)
  }

  /**
   * Handle callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    if (callbackData.startsWith("export_confirm:")) {
      const exportWalletId = parseInt(callbackData.split(":")[1] || "0")
      if (!isNaN(exportWalletId) && exportWalletId > 0) {
        const telegramId = ctx.from?.id as number
        const wallet = await Wallet.getByIdForOwner(exportWalletId, telegramId)
        if (wallet) {
          await this.showPrivateKey(ctx, wallet, telegramId)
          return true
        }
      }
    }
    return false
  }

  /**
   * Show export confirmation with security warning
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  async showConfirmation(ctx: any, wallet: any): Promise<void> {
    const keyboard = WalletExportPage.createExportConfirmationKeyboard(wallet.id)

    const { chainDisplayName } = BlockchainConfig.get[wallet.chain]
    await BotUtils.updateKeyboard(ctx, "export_confirmation", keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }

  /**
   * Show private key with security warnings
   * @param ctx Callback query context
   * @param wallet Wallet object
   * @param telegramId User's Telegram ID for ownership validation
   */
  async showPrivateKey(ctx: any, wallet: any, telegramId: number): Promise<void> {
    try {
      const privateKey = await Wallet.getDecryptedPrivateKey(wallet.id, telegramId, BotUtils.getPasswordWallet())

      if (!privateKey) {
        await this.showError(ctx, wallet)
        return
      }

      // Show the private key with security warnings
      const keyboard = WalletExportPage.createBackToWalletKeyboard(wallet.id)
      const { chainDisplayName } = BlockchainConfig.get[wallet.chain]

      await BotUtils.updateKeyboard(ctx, "export_private_key", keyboard, {
        walletName: wallet.name,
        chainName: chainDisplayName,
        address: wallet.address,
        privateKey
      })
    } catch (error) {
      log.error(`Error WalletExportPage.showPrivateKey: ${error}`)
      await this.showError(ctx, wallet)
    }
  }

  /**
   * Show export error page
   * @param ctx Callback query context
   * @param wallet Wallet object
   */
  private async showError(ctx: any, wallet: any): Promise<void> {
    const keyboard = WalletExportPage.createBackToWalletKeyboard(wallet.id)
    const { chainDisplayName } = BlockchainConfig.get[wallet.chain]

    await BotUtils.updateKeyboard(ctx, "export_error", keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      address: wallet.address
    })
  }
}
