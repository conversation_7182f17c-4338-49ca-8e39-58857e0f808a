import { InlineKeyboard } from "grammy"
import { BotUtils } from "../baseHandler"
import { User, Wallet } from "../../models"
import { BlockchainConfig } from "../../blockchain/config"
import { log } from "../../utils/log"

/**
 * Wallet List Page
 * Handles wallet listing with pagination
 */
export class WalletListPage {
  /**
   * Create wallet list keyboard with pagination
   * @param wallets Array of wallet objects
   * @param currentPage Current page number (0-based)
   * @param totalPages Total number of pages
   */
  private static createKeyboard(wallets: any[], currentPage: number, totalPages: number): InlineKeyboard {
    const keyboard = new InlineKeyboard()

    // Add wallet buttons
    wallets.forEach((wallet) => {
      const displayName = wallet.displayName || `💼 ${wallet.name}`
      keyboard.text(displayName, `wallet_select:${wallet.id}`).row()
    })

    // Add pagination if needed
    if (totalPages > 1) {
      keyboard.row()
      if (currentPage > 0) {
        keyboard.text("⬅️ Previous", `page:wallets:${currentPage - 1}`)
      }
      keyboard.text(`${currentPage + 1}/${totalPages}`, "noop")
      if (currentPage < totalPages - 1) {
        keyboard.text("➡️ Next", `page:wallets:${currentPage + 1}`)
      }
    }

    // Add action buttons
    keyboard.row().text("➕ Create", "wallet_action:create").text("📥 Import", "wallet_action:import").text("🔙 Menu", "back:main_menu")

    return keyboard
  }

  /**
   * Create empty wallet state keyboard
   */
  private static createEmptyKeyboard(): InlineKeyboard {
    return new InlineKeyboard().text("➕ Create Wallet", "wallet_action:create").text("📥 Import Wallet", "wallet_action:import").row().text("🔙 Back to Menu", "back:main_menu")
  }

  /**
   * Handle callback queries for this page
   * @param ctx Callback query context
   * @param callbackData Complete callback data string
   * @returns true if handled, false if not applicable
   */
  async handleCallback(ctx: any, callbackData: string): Promise<boolean> {
    if (callbackData === "wallet_action:list" || callbackData === "back:wallets") {
      await this.show(ctx)
      return true
    } else if (callbackData.startsWith("page:wallets:")) {
      const parts = callbackData.split(":")
      const page = parseInt(parts[2] || "0")
      if (!isNaN(page)) {
        await this.show(ctx, page)
        return true
      }
    }
    return false
  }
  /**
   * Show wallet list with pagination
   * @param ctx Callback query context
   * @param page Page number (0-based, default: 0)
   */
  async show(ctx: any, page: number = 0): Promise<void> {
    try {
      const telegramId = ctx.from?.id || 0
      const user = await User.getById(telegramId)

      if (!user) {
        await BotUtils.answerCallback(ctx, "callback_user_not_found")
        await BotUtils.deleteKeyboard(ctx)
        return
      }

      const wallets = await Wallet.getAllForOwner(user.id)

      if (wallets.length === 0) {
        await this.showEmptyState(ctx)
        return
      }

      await this.showWalletList(ctx, wallets, page)
    } catch (error) {
      log.error(`Error WalletListPage.show: ${error}`)
      await BotUtils.deleteKeyboard(ctx)
      await BotUtils.answerCallback(ctx, "callback_error_general")
    }
  }

  /**
   * Show empty wallet state
   * @param ctx Callback query context
   */
  private async showEmptyState(ctx: any): Promise<void> {
    const keyboard = WalletListPage.createEmptyKeyboard()
    await BotUtils.updateKeyboard(ctx, "wallets_empty", keyboard)
  }

  /**
   * Show wallet list with pagination
   * @param ctx Callback query context
   * @param wallets Array of wallets
   * @param page Current page number
   */
  private async showWalletList(ctx: any, wallets: any[], page: number): Promise<void> {
    // Add wallet buttons (max 5 per page for better UX)
    const walletsPerPage = 5
    const totalPages = Math.ceil(wallets.length / walletsPerPage)
    const currentPage = Math.max(0, Math.min(page, totalPages - 1))
    const startIndex = currentPage * walletsPerPage
    const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

    // Get wallets for current page with balance info
    const pageWallets = wallets.slice(startIndex, endIndex).map((wallet: any) => {
      const balance = wallet.balance.toString()
      const { symbol } = BlockchainConfig.get[wallet.chain]
      return {
        ...wallet,
        displayName: `${wallet.name} (${balance} ${symbol})`
      }
    })

    const keyboard = WalletListPage.createKeyboard(pageWallets, currentPage, totalPages)

    // Format wallet list for display
    const walletList = wallets
      .slice(startIndex, endIndex)
      .map((wallet, index) => {
        const balance = wallet.balance.toString()
        const { symbol } = BlockchainConfig.get[wallet.chain]
        return `${startIndex + index + 1}. ${wallet.name}\n   🔗 ${wallet.chain}\n   💰 ${balance} ${symbol}\n   📍 ${wallet.address}`
      })
      .join("\n\n")

    await BotUtils.updateKeyboard(ctx, "wallets_list", keyboard, {
      walletList,
      totalWallets: wallets.length,
      currentPage: currentPage + 1,
      totalPages
    })
  }
}
