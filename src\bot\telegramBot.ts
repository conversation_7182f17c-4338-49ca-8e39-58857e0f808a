import { <PERSON><PERSON>, InlineKeyboard } from "grammy"
import { log } from "../utils/log"
import { User, Wallet } from "../models"
import { CallbackDistributor } from "./callbackDistributor"
import { BotUtils } from "./baseHandler"

/**
 * Production-optimized Telegram bot class
 * Consolidated bot implementation with distributed callback handling for maximum maintainability
 * Eliminates inheritance complexity while preserving all functionality
 */
export class TelegramBot {
  private bot: Bot

  // Callback distributor for enhanced maintainability
  private callbackDistributor = new CallbackDistributor()

  // Convenient access to main menu page
  private get mainMenuPage() {
    return this.callbackDistributor.mainMenuPage
  }

  constructor() {
    this.bot = new Bot(process.env.TELEGRAM_TOKEN as string)
    this.initializeBot()
  }

  /**
   * Initialize bot with event handlers
   */
  private initializeBot(): void {
    // Handle callback queries from inline keyboards
    this.bot.on("callback_query:data", this.handleCallbackQuery.bind(this))

    // Handle text messages
    this.bot.on("message:text", this.handleTextMessage.bind(this))

    // Error handling
    this.bot.catch((err) => {
      log.error(`Telegram bot error: ${JSON.stringify(err)}`)
    })

    // Start bot
    this.bot.start({
      onStart(botInfo) {
        log.info(`Telegram bot started: ${botInfo.username}`)
      }
    })
  }

  /**
   * Handle callback queries from inline keyboards
   * @param ctx Callback query context
   */
  private async handleCallbackQuery(ctx: any): Promise<void> {
    try {
      await ctx.answerCallbackQuery()

      const callbackData = ctx.callbackQuery.data

      // Use callback distributor to handle the callback
      await this.onCallbackQuery(ctx, callbackData)
    } catch (error) {
      log.error(`Error handleCallbackQuery: ${error}`)
      await BotUtils.answerCallback(ctx, "callback_error_general")
      await BotUtils.deleteKeyboard(ctx)
    }
  }

  /**
   * Callback query handler with distributed callback handling
   */
  private async onCallbackQuery(ctx: any, callbackData: string): Promise<void> {
    try {
      // Use callback distributor to handle the callback
      const handled = await this.callbackDistributor.distributeCallback(ctx, callbackData)

      if (!handled) {
        log.warn(`Unknown callback data: ${callbackData}`)
        await BotUtils.deleteKeyboard(ctx)
      }
    } catch (error) {
      log.error(`Error in onCallbackQuery: ${error}`)
      await BotUtils.deleteKeyboard(ctx)
    }
  }

  /**
   * Handle text messages
   * @param ctx Message context
   */
  private async handleTextMessage(ctx: any): Promise<void> {
    try {
      const telegramId = ctx.from?.id as number
      const user = await this.getOrCreateUser(telegramId, ctx.from?.username, ctx.from?.first_name)

      if (!user) {
        const keyboard = new InlineKeyboard().text("🔄 Try Again", "main_menu")
        await BotUtils.replyWithKeyboard(ctx, "user_creation_failed", keyboard)
        return
      }

      // Check if user has an active session
      const hasSession = await BotUtils.sessionHas(telegramId)
      if (hasSession) {
        await this.onSessionInput(ctx, telegramId)
        return
      }

      // No active session - show main menu
      await this.onTextMessage(ctx, user)
    } catch (error) {
      log.error(`Error handleTextMessage: ${error}`)
      const keyboard = new InlineKeyboard().text("🏠 Back to Main Menu", "main_menu")
      await BotUtils.replyWithKeyboard(ctx, "general_error", keyboard)
    }
  }

  /**
   * Get or create user
   * @param telegramId Telegram user ID
   * @param username Username
   * @param firstName First name
   * @returns User object or null
   */
  private async getOrCreateUser(telegramId: number, username?: string, firstName?: string): Promise<any> {
    return await User.getOrCreate(telegramId, username || `user_${telegramId}`, firstName || "Unknown")
  }

  /**
   * Handle session input
   * @param ctx Message context
   * @param userId User ID
   */
  private async onSessionInput(ctx: any, userId: number): Promise<void> {
    const session = await BotUtils.sessionGet(userId)
    if (!session || !session.state) {
      await BotUtils.sessionDelete(userId)
      await this.onNoSession(ctx)
      return
    }

    const userInput = ctx.message?.text?.trim()
    if (!userInput) {
      const keyboard = new InlineKeyboard().text("❌ Cancel", "cancel")
      await BotUtils.replyWithKeyboard(ctx, "input_invalid_empty", keyboard)
      return
    }

    // Handle session input
    await this.onSessionInputReceived(ctx, userId, userInput, session)
  }

  /**
   * Text message handler - show main menu for users without active sessions
   */
  private async onTextMessage(ctx: any, _user: any): Promise<void> {
    // Show main menu for users without active sessions
    await this.mainMenuPage.show(ctx)
  }

  /**
   * No session handler - show main menu
   */
  private async onNoSession(ctx: any): Promise<void> {
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Session input handler - process user input based on session state
   */
  private async onSessionInputReceived(ctx: any, userId: number, userInput: string, session: any): Promise<void> {
    switch (session.state) {
      case "waiting_wallet_name":
        await this.callbackDistributor.walletCreatePage.processCreation(ctx, userId, userInput, session.chain)
        break
      case "waiting_private_key":
        // Process private key input for wallet import
        await this.callbackDistributor.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        break
      case "waiting_import_details":
        // Handle multi-step import process (wallet name or private key)
        const step = session.step || "wallet_name"
        if (step === "wallet_name") {
          // Process wallet name and move to private key step
          await this.handleWalletNameInput(ctx, userId, userInput, session)
        } else if (step === "private_key") {
          // Process private key input
          await this.callbackDistributor.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        }
        break
      default:
        log.warn(`Unknown session state: ${session.state}`)
        await BotUtils.sessionDelete(userId)
        await this.mainMenuPage.show(ctx)
    }
  }

  /**
   * Handle wallet name input during import process
   * @param ctx Message context
   * @param userId User ID
   * @param walletName Wallet name input
   * @param session Session data
   */
  private async handleWalletNameInput(ctx: any, userId: number, walletName: string, session: any): Promise<void> {
    // Validate wallet name
    if (walletName.length > 32) {
      await this.callbackDistributor.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(walletName)) {
      await this.callbackDistributor.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    // Check if wallet name already exists
    const user = await User.getById(userId)
    if (!user) {
      await BotUtils.sessionDelete(userId)
      await this.mainMenuPage.show(ctx)
      return
    }

    const existingWallet = await Wallet.getByName(user.id, walletName)
    if (existingWallet) {
      await this.callbackDistributor.walletImportPage.showFailure(ctx, session.chain, "duplicate_name")
      return
    }

    // Update session and show private key prompt
    await BotUtils.sessionSet(userId, {
      ...session,
      walletName: walletName,
      step: "private_key"
    })

    await this.callbackDistributor.walletImportPage.showPrivateKeyPrompt(ctx, session.chain, walletName, userId)
  }
}
