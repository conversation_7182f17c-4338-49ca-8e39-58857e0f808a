import { log } from "../utils/log"
import { User, Wallet } from "../models"
import { TelegramHandler } from "./handlers/telegram"
import { CallbackDistributor } from "./callbackDistributor"
import { BotUtils } from "./baseHandler"

/**
 * Production-optimized Telegram bot class
 * Enhanced with distributed callback handling for maximum maintainability
 */
export class TelegramBot extends TelegramHandler {
  // Callback distributor for enhanced maintainability
  private callbackDistributor = new CallbackDistributor()

  // Convenient access to main menu page
  private get mainMenuPage() {
    return this.callbackDistributor.mainMenuPage
  }

  constructor() {
    super(process.env.TELEGRAM_TOKEN as string)
  }

  /**
   * Override callback query handler with distributed callback handling
   */
  protected async onCallbackQuery(ctx: any, callbackData: string): Promise<void> {
    try {
      // Use callback distributor to handle the callback
      const handled = await this.callbackDistributor.distributeCallback(ctx, callbackData)

      if (!handled) {
        log.warn(`Unknown callback data: ${callbackData}`)
        await BotUtils.deleteKeyboard(ctx)
      }
    } catch (error) {
      log.error(`Error in onCallbackQuery: ${error}`)
      await BotUtils.deleteKeyboard(ctx)
    }
  }

  /**
   * Override text message handler
   */
  protected async onTextMessage(ctx: any, _user: any): Promise<void> {
    // Show main menu for users without active sessions
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Override no session handler
   */
  protected async onNoSession(ctx: any): Promise<void> {
    await this.mainMenuPage.show(ctx)
  }

  /**
   * Override session input handler
   */
  protected async onSessionInputReceived(ctx: any, userId: number, userInput: string, session: any): Promise<void> {
    switch (session.state) {
      case "waiting_wallet_name":
        await this.callbackDistributor.walletCreatePage.processCreation(ctx, userId, userInput, session.chain)
        break
      case "waiting_private_key":
        // Process private key input for wallet import
        await this.callbackDistributor.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        break
      case "waiting_import_details":
        // Handle multi-step import process (wallet name or private key)
        const step = session.step || "wallet_name"
        if (step === "wallet_name") {
          // Process wallet name and move to private key step
          await this.handleWalletNameInput(ctx, userId, userInput, session)
        } else if (step === "private_key") {
          // Process private key input
          await this.callbackDistributor.walletImportPage.processImport(ctx, userId, session.walletName, userInput, session.chain)
        }
        break
      default:
        log.warn(`Unknown session state: ${session.state}`)
        await BotUtils.sessionDelete(userId)
        await this.mainMenuPage.show(ctx)
    }
  }

  /**
   * Handle wallet name input during import process
   * @param ctx Message context
   * @param userId User ID
   * @param walletName Wallet name input
   * @param session Session data
   */
  private async handleWalletNameInput(ctx: any, userId: number, walletName: string, session: any): Promise<void> {
    // Validate wallet name
    if (walletName.length > 32) {
      await this.callbackDistributor.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(walletName)) {
      await this.callbackDistributor.walletImportPage.showFailure(ctx, session.chain, "invalid_name")
      return
    }

    // Check if wallet name already exists
    const user = await User.getById(userId)
    if (!user) {
      await BotUtils.sessionDelete(userId)
      await this.mainMenuPage.show(ctx)
      return
    }

    const existingWallet = await Wallet.getByName(user.id, walletName)
    if (existingWallet) {
      await this.callbackDistributor.walletImportPage.showFailure(ctx, session.chain, "duplicate_name")
      return
    }

    // Update session and show private key prompt
    await BotUtils.sessionSet(userId, {
      ...session,
      walletName: walletName,
      step: "private_key"
    })

    await this.callbackDistributor.walletImportPage.showPrivateKeyPrompt(ctx, session.chain, walletName, userId)
  }
}
